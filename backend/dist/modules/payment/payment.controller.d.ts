import { Request, Response, NextFunction } from 'express';
import { AuthRequest } from '../../middleware/auth';
export declare class PaymentController {
    private static getIyzicoService;
    static initializeSubscriptionPayment(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static handlePaymentCallback(req: Request, res: Response, next: NextFunction): Promise<void>;
    static getPaymentStatus(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static cancelPayment(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static refundPayment(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getInstallmentInfo(req: Request, res: Response, next: NextFunction): Promise<void>;
    static testIyzicoConnection(req: Request, res: Response, next: NextFunction): Promise<void>;
    static initializeProformaPayment(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static handleProformaPaymentCallback(req: Request, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=payment.controller.d.ts.map