"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentController = void 0;
const iyzico_service_1 = require("./iyzico.service");
const service_1 = require("../subscription/service");
const payment_validation_1 = require("./payment.validation");
class PaymentController {
    static getIyzicoService() {
        const config = {
            apiKey: process.env.IYZICO_API_KEY || '',
            secretKey: process.env.IYZICO_SECRET_KEY || '',
            uri: process.env.IYZICO_URI || 'https://sandbox-api.iyzipay.com'
        };
        if (!config.apiKey || !config.secretKey) {
            throw new Error('iyzico credentials not configured');
        }
        return new iyzico_service_1.IyzicoService(config);
    }
    static async initializeSubscriptionPayment(req, res, next) {
        try {
            const { error } = payment_validation_1.paymentValidationSchemas.initializeSubscriptionPayment.validate(req.body);
            if (error) {
                res.status(400).json({
                    success: false,
                    error: error.details[0].message
                });
                return;
            }
            const { subscriptionType } = req.body;
            const userId = req.user._id;
            const userInfo = req.user;
            const ip = req.ip || req.connection.remoteAddress || '127.0.0.1';
            const iyzicoService = PaymentController.getIyzicoService();
            const paymentData = iyzico_service_1.IyzicoService.prepareSubscriptionPaymentData(userId, subscriptionType, userInfo, ip);
            const result = await iyzicoService.initializeCheckoutForm(paymentData);
            if (result.status === 'success') {
                res.status(200).json({
                    success: true,
                    data: {
                        checkoutFormContent: result.checkoutFormContent,
                        token: result.token,
                        paymentPageUrl: result.paymentPageUrl
                    },
                    message: 'Ödeme formu başarıyla oluşturuldu'
                });
            }
            else {
                res.status(400).json({
                    success: false,
                    error: result.errorMessage || 'Ödeme formu oluşturulamadı'
                });
            }
        }
        catch (error) {
            next(error);
        }
    }
    static async handlePaymentCallback(req, res, next) {
        try {
            const { token } = req.body;
            if (!token) {
                res.status(400).json({
                    success: false,
                    error: 'Token gerekli'
                });
                return;
            }
            const iyzicoService = PaymentController.getIyzicoService();
            const result = await iyzicoService.retrieveCheckoutForm(token);
            if (result.status === 'success' && result.paymentStatus === 'SUCCESS') {
                const conversationId = result.conversationId;
                const userId = conversationId.split('_')[1];
                const subscriptionType = result.basketItems[0].name.includes('Aylık') ? 'monthly' : 'yearly';
                const subscription = await service_1.SubscriptionService.createSubscription(userId, {
                    subscriptionType: subscriptionType,
                    paymentDetails: {
                        amount: parseFloat(result.paidPrice),
                        currency: result.currency,
                        transactionId: result.paymentId,
                        paymentMethod: 'iyzico'
                    }
                });
                res.status(200).json({
                    success: true,
                    data: {
                        subscription,
                        paymentResult: result
                    },
                    message: 'Ödeme başarılı, abonelik oluşturuldu'
                });
            }
            else {
                res.status(400).json({
                    success: false,
                    error: result.errorMessage || 'Ödeme başarısız',
                    data: result
                });
            }
        }
        catch (error) {
            next(error);
        }
    }
    static async getPaymentStatus(req, res, next) {
        try {
            const { paymentId, conversationId } = req.params;
            const iyzicoService = PaymentController.getIyzicoService();
            const result = await iyzicoService.retrievePayment(paymentId, conversationId);
            res.status(200).json({
                success: true,
                data: result
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async cancelPayment(req, res, next) {
        try {
            const { error } = payment_validation_1.paymentValidationSchemas.cancelPayment.validate(req.body);
            if (error) {
                res.status(400).json({
                    success: false,
                    error: error.details[0].message
                });
                return;
            }
            const { paymentId } = req.body;
            const ip = req.ip || req.connection.remoteAddress || '127.0.0.1';
            const iyzicoService = PaymentController.getIyzicoService();
            const result = await iyzicoService.cancelPayment(paymentId, ip);
            if (result.status === 'success') {
                res.status(200).json({
                    success: true,
                    data: result,
                    message: 'Ödeme başarıyla iptal edildi'
                });
            }
            else {
                res.status(400).json({
                    success: false,
                    error: result.errorMessage || 'Ödeme iptal edilemedi'
                });
            }
        }
        catch (error) {
            next(error);
        }
    }
    static async refundPayment(req, res, next) {
        try {
            const { error } = payment_validation_1.paymentValidationSchemas.refundPayment.validate(req.body);
            if (error) {
                res.status(400).json({
                    success: false,
                    error: error.details[0].message
                });
                return;
            }
            const { paymentTransactionId, price } = req.body;
            const ip = req.ip || req.connection.remoteAddress || '127.0.0.1';
            const iyzicoService = PaymentController.getIyzicoService();
            const result = await iyzicoService.refundPayment(paymentTransactionId, price, ip);
            if (result.status === 'success') {
                res.status(200).json({
                    success: true,
                    data: result,
                    message: 'İade işlemi başarıyla tamamlandı'
                });
            }
            else {
                res.status(400).json({
                    success: false,
                    error: result.errorMessage || 'İade işlemi başarısız'
                });
            }
        }
        catch (error) {
            next(error);
        }
    }
    static async getInstallmentInfo(req, res, next) {
        try {
            const { binNumber, price } = req.query;
            if (!binNumber || !price) {
                res.status(400).json({
                    success: false,
                    error: 'BIN numarası ve fiyat gerekli'
                });
                return;
            }
            const iyzicoService = PaymentController.getIyzicoService();
            const result = await iyzicoService.retrieveInstallmentInfo(binNumber, price);
            res.status(200).json({
                success: true,
                data: result
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async testIyzicoConnection(req, res, next) {
        try {
            const iyzicoService = PaymentController.getIyzicoService();
            const testResult = await iyzicoService.retrieveInstallmentInfo('554960', '100');
            res.status(200).json({
                success: true,
                message: 'iyzico bağlantısı başarılı',
                data: testResult
            });
        }
        catch (error) {
            res.status(500).json({
                success: false,
                error: 'iyzico bağlantısı başarısız',
                details: error instanceof Error ? error.message : 'Bilinmeyen hata'
            });
        }
    }
    static async initializeProformaPayment(req, res, next) {
        try {
            const { error } = payment_validation_1.paymentValidationSchemas.initializeProformaPayment.validate(req.body);
            if (error) {
                res.status(400).json({
                    success: false,
                    error: error.details[0].message
                });
                return;
            }
            const { calculationId, amount, currency = 'TRY' } = req.body;
            const userId = req.user._id;
            const userInfo = req.user;
            const ip = req.ip || req.connection.remoteAddress || '127.0.0.1';
            const iyzicoService = PaymentController.getIyzicoService();
            const paymentData = iyzico_service_1.IyzicoService.prepareProformaPaymentData(userId, calculationId, amount, currency, userInfo, ip);
            const result = await iyzicoService.initializeCheckoutForm(paymentData);
            if (result.status === 'success') {
                res.status(200).json({
                    success: true,
                    data: {
                        checkoutFormContent: result.checkoutFormContent,
                        token: result.token,
                        paymentPageUrl: result.paymentPageUrl
                    },
                    message: 'Proforma ödeme formu başarıyla oluşturuldu'
                });
            }
            else {
                res.status(400).json({
                    success: false,
                    error: result.errorMessage || 'Ödeme formu oluşturulamadı'
                });
            }
        }
        catch (error) {
            next(error);
        }
    }
    static async handleProformaPaymentCallback(req, res, next) {
        try {
            const { token } = req.body;
            if (!token) {
                res.status(400).json({
                    success: false,
                    error: 'Token gerekli'
                });
                return;
            }
            const iyzicoService = PaymentController.getIyzicoService();
            const result = await iyzicoService.retrieveCheckoutForm(token);
            if (result.status === 'success' && result.paymentStatus === 'SUCCESS') {
                const conversationId = result.conversationId;
                const calculationId = conversationId.split('_')[2];
                res.status(200).json({
                    success: true,
                    data: {
                        calculationId,
                        paymentResult: result,
                        amount: parseFloat(result.paidPrice),
                        currency: result.currency,
                        transactionId: result.paymentId
                    },
                    message: 'Proforma ödeme başarılı'
                });
            }
            else {
                res.status(400).json({
                    success: false,
                    error: result.errorMessage || 'Ödeme başarısız',
                    data: result
                });
            }
        }
        catch (error) {
            next(error);
        }
    }
}
exports.PaymentController = PaymentController;
//# sourceMappingURL=payment.controller.js.map