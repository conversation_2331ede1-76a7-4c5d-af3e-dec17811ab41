"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const payment_controller_1 = require("./payment.controller");
const auth_1 = require("../../middleware/auth");
const router = (0, express_1.Router)();
router.post('/callback', payment_controller_1.PaymentController.handlePaymentCallback);
router.post('/proforma/callback', payment_controller_1.PaymentController.handleProformaPaymentCallback);
router.get('/installment-info', payment_controller_1.PaymentController.getInstallmentInfo);
if (process.env.NODE_ENV === 'development') {
    router.get('/test-connection', payment_controller_1.PaymentController.testIyzicoConnection);
}
router.use(auth_1.protect);
router.post('/subscription/initialize', payment_controller_1.PaymentController.initializeSubscriptionPayment);
router.get('/status/:paymentId/:conversationId', payment_controller_1.PaymentController.getPaymentStatus);
router.post('/proforma/initialize', payment_controller_1.PaymentController.initializeProformaPayment);
router.post('/cancel', payment_controller_1.PaymentController.cancelPayment);
router.post('/refund', payment_controller_1.PaymentController.refundPayment);
exports.default = router;
//# sourceMappingURL=payment.routes.js.map