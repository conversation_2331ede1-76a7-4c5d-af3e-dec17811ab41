export interface IyzicoConfig {
    apiKey: string;
    secretKey: string;
    uri: string;
}
export interface PaymentRequest {
    price: string;
    paidPrice: string;
    currency: string;
    installment: string;
    basketId: string;
    conversationId: string;
    buyer: {
        id: string;
        name: string;
        surname: string;
        gsmNumber: string;
        email: string;
        identityNumber: string;
        registrationAddress: string;
        ip: string;
        city: string;
        country: string;
        zipCode: string;
    };
    shippingAddress: {
        contactName: string;
        city: string;
        country: string;
        address: string;
        zipCode: string;
    };
    billingAddress: {
        contactName: string;
        city: string;
        country: string;
        address: string;
        zipCode: string;
    };
    basketItems: Array<{
        id: string;
        name: string;
        category1: string;
        category2: string;
        itemType: string;
        price: string;
    }>;
    paymentCard: {
        cardHolderName: string;
        cardNumber: string;
        expireMonth: string;
        expireYear: string;
        cvc: string;
        registerCard: string;
    };
}
export interface CheckoutFormRequest {
    locale: string;
    conversationId: string;
    price: string;
    paidPrice: string;
    currency: string;
    basketId: string;
    paymentGroup: string;
    callbackUrl: string;
    enabledInstallments: string[];
    buyer: {
        id: string;
        name: string;
        surname: string;
        gsmNumber: string;
        email: string;
        identityNumber: string;
        registrationAddress: string;
        ip: string;
        city: string;
        country: string;
        zipCode: string;
    };
    shippingAddress: {
        contactName: string;
        city: string;
        country: string;
        address: string;
        zipCode: string;
    };
    billingAddress: {
        contactName: string;
        city: string;
        country: string;
        address: string;
        zipCode: string;
    };
    basketItems: Array<{
        id: string;
        name: string;
        category1: string;
        category2: string;
        itemType: string;
        price: string;
    }>;
}
export declare class IyzicoService {
    private iyzipay;
    constructor(config: IyzicoConfig);
    createPayment(request: PaymentRequest): Promise<any>;
    initializeCheckoutForm(request: CheckoutFormRequest): Promise<any>;
    retrieveCheckoutForm(token: string): Promise<any>;
    retrievePayment(paymentId: string, conversationId: string): Promise<any>;
    cancelPayment(paymentId: string, ip: string): Promise<any>;
    refundPayment(paymentTransactionId: string, price: string, ip: string): Promise<any>;
    retrieveInstallmentInfo(binNumber: string, price: string): Promise<any>;
    static prepareSubscriptionPaymentData(userId: string, subscriptionType: 'monthly' | 'yearly', userInfo: any, ip: string): CheckoutFormRequest;
    static prepareProformaPaymentData(userId: string, calculationId: string, amount: number, currency: string | undefined, userInfo: any, ip: string): CheckoutFormRequest;
}
//# sourceMappingURL=iyzico.service.d.ts.map