"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.paymentValidationSchemas = void 0;
const joi_1 = __importDefault(require("joi"));
exports.paymentValidationSchemas = {
    initializeSubscriptionPayment: joi_1.default.object({
        subscriptionType: joi_1.default.string()
            .valid('monthly', 'yearly')
            .required()
            .messages({
            'any.only': 'Abonelik türü monthly veya yearly olmalıdır',
            'any.required': 'Abonelik türü zorunludur'
        })
    }),
    cancelPayment: joi_1.default.object({
        paymentId: joi_1.default.string()
            .required()
            .messages({
            'any.required': 'Ödeme ID zorunludur',
            'string.base': 'Ödeme ID string olmalıdır'
        })
    }),
    refundPayment: joi_1.default.object({
        paymentTransactionId: joi_1.default.string()
            .required()
            .messages({
            'any.required': 'Ödeme işlem ID zorunludur',
            'string.base': 'Ödeme işlem ID string olmalıdır'
        }),
        price: joi_1.default.string()
            .required()
            .pattern(/^\d+(\.\d{1,2})?$/)
            .messages({
            'any.required': 'İade tutarı zorunludur',
            'string.pattern.base': 'İade tutarı geçerli bir fiyat formatında olmalıdır (örn: 99.99)'
        })
    }),
    paymentCallback: joi_1.default.object({
        token: joi_1.default.string()
            .required()
            .messages({
            'any.required': 'Token zorunludur',
            'string.base': 'Token string olmalıdır'
        })
    }),
    directPayment: joi_1.default.object({
        subscriptionType: joi_1.default.string()
            .valid('monthly', 'yearly')
            .required()
            .messages({
            'any.only': 'Abonelik türü monthly veya yearly olmalıdır',
            'any.required': 'Abonelik türü zorunludur'
        }),
        paymentCard: joi_1.default.object({
            cardHolderName: joi_1.default.string()
                .min(2)
                .max(50)
                .required()
                .messages({
                'string.min': 'Kart sahibi adı en az 2 karakter olmalıdır',
                'string.max': 'Kart sahibi adı en fazla 50 karakter olmalıdır',
                'any.required': 'Kart sahibi adı zorunludur'
            }),
            cardNumber: joi_1.default.string()
                .pattern(/^\d{16}$/)
                .required()
                .messages({
                'string.pattern.base': 'Kart numarası 16 haneli olmalıdır',
                'any.required': 'Kart numarası zorunludur'
            }),
            expireMonth: joi_1.default.string()
                .pattern(/^(0[1-9]|1[0-2])$/)
                .required()
                .messages({
                'string.pattern.base': 'Son kullanma ayı 01-12 arasında olmalıdır',
                'any.required': 'Son kullanma ayı zorunludur'
            }),
            expireYear: joi_1.default.string()
                .pattern(/^\d{4}$/)
                .required()
                .messages({
                'string.pattern.base': 'Son kullanma yılı 4 haneli olmalıdır',
                'any.required': 'Son kullanma yılı zorunludur'
            }),
            cvc: joi_1.default.string()
                .pattern(/^\d{3,4}$/)
                .required()
                .messages({
                'string.pattern.base': 'CVC 3 veya 4 haneli olmalıdır',
                'any.required': 'CVC zorunludur'
            }),
            registerCard: joi_1.default.string()
                .valid('0', '1')
                .default('0')
                .messages({
                'any.only': 'Kart kaydetme seçeneği 0 veya 1 olmalıdır'
            })
        }).required().messages({
            'any.required': 'Kart bilgileri zorunludur'
        }),
        installment: joi_1.default.string()
            .valid('1', '2', '3', '6', '9', '12')
            .default('1')
            .messages({
            'any.only': 'Taksit sayısı 1, 2, 3, 6, 9 veya 12 olmalıdır'
        })
    }),
    getInstallmentInfo: joi_1.default.object({
        binNumber: joi_1.default.string()
            .pattern(/^\d{6}$/)
            .required()
            .messages({
            'string.pattern.base': 'BIN numarası 6 haneli olmalıdır',
            'any.required': 'BIN numarası zorunludur'
        }),
        price: joi_1.default.string()
            .pattern(/^\d+(\.\d{1,2})?$/)
            .required()
            .messages({
            'string.pattern.base': 'Fiyat geçerli bir format olmalıdır (örn: 99.99)',
            'any.required': 'Fiyat zorunludur'
        })
    }),
    initializeProformaPayment: joi_1.default.object({
        calculationId: joi_1.default.string()
            .required()
            .messages({
            'any.required': 'Hesaplama ID zorunludur',
            'string.base': 'Hesaplama ID string olmalıdır'
        }),
        amount: joi_1.default.number()
            .positive()
            .required()
            .messages({
            'number.positive': 'Tutar pozitif bir sayı olmalıdır',
            'any.required': 'Tutar zorunludur'
        }),
        currency: joi_1.default.string()
            .valid('TRY', 'USD', 'EUR')
            .default('TRY')
            .messages({
            'any.only': 'Para birimi TRY, USD veya EUR olmalıdır'
        })
    })
};
//# sourceMappingURL=payment.validation.js.map