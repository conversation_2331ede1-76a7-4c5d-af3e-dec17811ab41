import Joi from 'joi';
export declare const paymentValidationSchemas: {
    initializeSubscriptionPayment: Joi.ObjectSchema<any>;
    cancelPayment: Joi.ObjectSchema<any>;
    refundPayment: Joi.ObjectSchema<any>;
    paymentCallback: Jo<PERSON>.ObjectSchema<any>;
    directPayment: Joi.ObjectSchema<any>;
    getInstallmentInfo: Joi.ObjectSchema<any>;
    initializeProformaPayment: Joi.ObjectSchema<any>;
};
//# sourceMappingURL=payment.validation.d.ts.map