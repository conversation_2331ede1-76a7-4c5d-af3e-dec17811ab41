"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IyzicoService = void 0;
const iyzipay_1 = __importDefault(require("iyzipay"));
class IyzicoService {
    constructor(config) {
        this.iyzipay = new iyzipay_1.default({
            apiKey: config.apiKey,
            secretKey: config.secretKey,
            uri: config.uri
        });
    }
    async createPayment(request) {
        return new Promise((resolve, reject) => {
            this.iyzipay.payment.create(request, (err, result) => {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(result);
                }
            });
        });
    }
    async initializeCheckoutForm(request) {
        return new Promise((resolve, reject) => {
            this.iyzipay.checkoutFormInitialize.create(request, (err, result) => {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(result);
                }
            });
        });
    }
    async retrieveCheckoutForm(token) {
        return new Promise((resolve, reject) => {
            this.iyzipay.checkoutForm.retrieve({
                locale: 'tr',
                token: token
            }, (err, result) => {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(result);
                }
            });
        });
    }
    async retrievePayment(paymentId, conversationId) {
        return new Promise((resolve, reject) => {
            this.iyzipay.payment.retrieve({
                locale: 'tr',
                conversationId: conversationId,
                paymentId: paymentId
            }, (err, result) => {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(result);
                }
            });
        });
    }
    async cancelPayment(paymentId, ip) {
        return new Promise((resolve, reject) => {
            this.iyzipay.cancel.create({
                locale: 'tr',
                paymentId: paymentId,
                ip: ip
            }, (err, result) => {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(result);
                }
            });
        });
    }
    async refundPayment(paymentTransactionId, price, ip) {
        return new Promise((resolve, reject) => {
            this.iyzipay.refund.create({
                locale: 'tr',
                paymentTransactionId: paymentTransactionId,
                price: price,
                ip: ip,
                currency: 'TRY'
            }, (err, result) => {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(result);
                }
            });
        });
    }
    async retrieveInstallmentInfo(binNumber, price) {
        return new Promise((resolve, reject) => {
            this.iyzipay.installmentInfo.retrieve({
                locale: 'tr',
                binNumber: binNumber,
                price: price
            }, (err, result) => {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(result);
                }
            });
        });
    }
    static prepareSubscriptionPaymentData(userId, subscriptionType, userInfo, ip) {
        const pricing = {
            monthly: { price: 99, name: 'Aylık Abonelik' },
            yearly: { price: 999, name: 'Yıllık Abonelik' }
        };
        const selectedPlan = pricing[subscriptionType];
        const conversationId = `SUB_${userId}_${Date.now()}`;
        const basketId = `BASKET_${conversationId}`;
        return {
            locale: 'tr',
            conversationId,
            price: selectedPlan.price.toString(),
            paidPrice: selectedPlan.price.toString(),
            currency: 'TRY',
            basketId,
            paymentGroup: 'SUBSCRIPTION',
            callbackUrl: `${process.env.FRONTEND_URL}/subscription/payment-callback`,
            enabledInstallments: ['1'],
            buyer: {
                id: userId,
                name: userInfo.name.split(' ')[0] || 'Ad',
                surname: userInfo.name.split(' ').slice(1).join(' ') || 'Soyad',
                gsmNumber: userInfo.phone || '+905350000000',
                email: userInfo.email,
                identityNumber: '11111111111',
                registrationAddress: userInfo.address || 'Test Adres',
                ip: ip,
                city: userInfo.city || 'Istanbul',
                country: 'Turkey',
                zipCode: userInfo.zipCode || '34000'
            },
            shippingAddress: {
                contactName: userInfo.name,
                city: userInfo.city || 'Istanbul',
                country: 'Turkey',
                address: userInfo.address || 'Test Adres',
                zipCode: userInfo.zipCode || '34000'
            },
            billingAddress: {
                contactName: userInfo.name,
                city: userInfo.city || 'Istanbul',
                country: 'Turkey',
                address: userInfo.address || 'Test Adres',
                zipCode: userInfo.zipCode || '34000'
            },
            basketItems: [
                {
                    id: `ITEM_${subscriptionType}`,
                    name: selectedPlan.name,
                    category1: 'Subscription',
                    category2: 'Software',
                    itemType: 'VIRTUAL',
                    price: selectedPlan.price.toString()
                }
            ]
        };
    }
    static prepareProformaPaymentData(userId, calculationId, amount, currency = 'TRY', userInfo, ip) {
        const conversationId = `CALC_${userId}_${calculationId}_${Date.now()}`;
        const basketId = `BASKET_${conversationId}`;
        return {
            locale: 'tr',
            conversationId,
            price: amount.toString(),
            paidPrice: amount.toString(),
            currency: currency,
            basketId,
            paymentGroup: 'CALCULATION',
            callbackUrl: `${process.env.FRONTEND_URL}/calculation/payment-callback`,
            enabledInstallments: ['1'],
            buyer: {
                id: userId,
                name: userInfo.name.split(' ')[0] || 'Ad',
                surname: userInfo.name.split(' ').slice(1).join(' ') || 'Soyad',
                gsmNumber: userInfo.phone || '+905350000000',
                email: userInfo.email,
                identityNumber: '11111111111',
                registrationAddress: userInfo.address || 'Test Adres',
                ip: ip,
                city: userInfo.city || 'Istanbul',
                country: 'Turkey',
                zipCode: userInfo.zipCode || '34000'
            },
            shippingAddress: {
                contactName: userInfo.name,
                city: userInfo.city || 'Istanbul',
                country: 'Turkey',
                address: userInfo.address || 'Test Adres',
                zipCode: userInfo.zipCode || '34000'
            },
            billingAddress: {
                contactName: userInfo.name,
                city: userInfo.city || 'Istanbul',
                country: 'Turkey',
                address: userInfo.address || 'Test Adres',
                zipCode: userInfo.zipCode || '34000'
            },
            basketItems: [
                {
                    id: `CALC_${calculationId}`,
                    name: 'Proforma Hesaplama',
                    category1: 'Calculation',
                    category2: 'Maritime',
                    itemType: 'VIRTUAL',
                    price: amount.toString()
                }
            ]
        };
    }
}
exports.IyzicoService = IyzicoService;
//# sourceMappingURL=iyzico.service.js.map