"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const dotenv_1 = __importDefault(require("dotenv"));
const database_1 = require("./config/database");
const routes_1 = __importDefault(require("./modules/auth/routes"));
const routes_2 = __importDefault(require("./modules/user/routes"));
const routes_3 = __importDefault(require("./modules/subscription/routes"));
const routes_4 = __importDefault(require("./modules/calculation/routes"));
const payment_routes_1 = __importDefault(require("./modules/payment/payment.routes"));
const errorHandler_1 = require("./middleware/errorHandler");
const clientDetection_1 = require("./middleware/clientDetection");
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 5000;
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true
}));
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000,
    max: 100
});
app.use(limiter);
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true }));
app.use((0, cookie_parser_1.default)());
app.use(clientDetection_1.detectClientType);
app.use('/api/auth', routes_1.default);
app.use('/api/users', routes_2.default);
app.use('/api/subscriptions', routes_3.default);
app.use('/api/calculations', routes_4.default);
app.use('/api/payments', payment_routes_1.default);
app.get('/health', (req, res) => {
    res.status(200).json({ status: 'OK', message: 'Server is running' });
});
app.use(errorHandler_1.errorHandler);
const startServer = async () => {
    try {
        await (0, database_1.connectDB)();
        console.log('MongoDB veritabanına bağlanıldı');
        app.listen(PORT, () => {
            console.log(`Sunucu ${PORT} portunda çalışıyor`);
        });
    }
    catch (error) {
        console.error('Sunucu başlatma hatası:', error);
        process.exit(1);
    }
};
startServer();
//# sourceMappingURL=server.js.map