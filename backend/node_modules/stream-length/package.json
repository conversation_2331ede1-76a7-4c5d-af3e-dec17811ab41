{"name": "stream-length", "version": "1.0.2", "description": "For a given Buffer or Stream, this module will attempt to determine the total length of the stream contents. It currently supports Buffers, `fs` streams, `http` responses, and `request` objects, and allows for specifying custom stream types.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git://github.com/joepie91/node-stream-length"}, "keywords": ["stream", "length", "content-length"], "author": "<PERSON>", "license": "WTFPL", "dependencies": {"bluebird": "^2.6.2"}, "devDependencies": {"request": "^2.51.0", "gulp": "~3.8.0", "gulp-cached": "~0.0.3", "gulp-coffee": "~2.0.1", "gulp-concat": "~2.2.0", "gulp-livereload": "~2.1.0", "gulp-nodemon": "~1.0.4", "gulp-plumber": "~0.6.3", "gulp-remember": "~0.2.0", "gulp-rename": "~1.2.0", "gulp-util": "~2.2.17"}}