{"name": "postman-request", "description": "Simplified HTTP request client.", "keywords": ["http", "simple", "util", "utility"], "version": "2.88.1-postman.42", "repository": {"type": "git", "url": "https://github.com/postmanlabs/postman-request.git"}, "license": "Apache-2.0", "engines": {"node": ">= 16"}, "main": "index.js", "files": ["lib/", "index.js", "request.js"], "dependencies": {"@postman/form-data": "~3.1.1", "@postman/tough-cookie": "~4.1.3-postman.1", "@postman/tunnel-agent": "^0.6.4", "aws-sign2": "~0.7.0", "aws4": "^1.12.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "http-signature": "~1.4.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "^2.1.35", "oauth-sign": "~0.9.0", "qs": "~6.5.3", "safe-buffer": "^5.1.2", "stream-length": "^1.0.2", "uuid": "^8.3.2"}, "scripts": {"test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js", "lint": "standard"}, "devDependencies": {"bluebird": "^3.2.1", "browserify": "^16.5.2", "browserify-istanbul": "^3.0.0", "buffer-equal": "^1.0.1", "codecov": "^3.0.4", "coveralls": "^3.0.2", "function-bind": "^1.0.2", "istanbul": "^0.4.0", "karma": "^3.1.4", "karma-browserify": "^5.0.1", "karma-chrome-launcher": "^3.1.1", "karma-cli": "^1.0.0", "karma-coverage": "^1.0.0", "karma-tap": "^3.0.1", "postman-url-encoder": "^3.0.6", "puppeteer": "^1.20.0", "rimraf": "^2.2.8", "server-destroy": "^1.0.1", "standard": "^9.0.0", "tape": "^4.6.0", "taper": "^0.5.0"}}