{"name": "extend", "author": "<PERSON> <<EMAIL>> (http://www.justmoon.net)", "version": "3.0.2", "description": "Port of jQuery.extend for node.js and the browser", "main": "index", "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "posttest": "npm run coverage-quiet", "tests-only": "node test", "coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs *.js */*.js", "eslint": "eslint *.js */*.js"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "keywords": ["extend", "clone", "merge"], "repository": {"type": "git", "url": "https://github.com/justmoon/node-extend.git"}, "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "covert": "^1.1.0", "eslint": "^4.19.1", "jscs": "^3.0.7", "tape": "^4.9.1"}, "license": "MIT"}