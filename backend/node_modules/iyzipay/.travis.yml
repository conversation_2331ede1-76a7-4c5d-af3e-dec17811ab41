language: node_js

node_js:
  - 0.10
  - 0.11
  - 0.12
  - 0.12.7
  - 4
  - 5
  - 6
  - 8
  - 10

before_install:
  - npm install istanbul -g
  - npm install coveralls -g
  - npm install mocha -g

deploy:
  provider: npm
  email: <EMAIL>
  api_key:
    secure: j3e1DiR61Wl11hKzrBVeff/B7V7yHnyc+WQaIXXFbjcuBQQizajzx9IrFaDxhxGoF5GXfTtFm62xk4TVKR8vYIpn0zTzYz6an31lqRmZybXA8llVjSs6M9dK/khRW1S1pds2aOavGZBpTU3IV2YppPm4//ORR7dImzs2guNVbpyNCkXI8iTvm+pCLiHFBBb0zBimrnbMIexXE8hr+MTUSDuLyN1fQz/gWNZB+jAG9HfQ+3wD1doGufUoo/7XtMmGLJlbsm+ElLo+nFcPcf/ZHoVQQguPy8EprPRDy02ARb4I0WiRWW0laX5roFVWLlHXXjCWRpkk4NdHJlTJycvzLVgwytliCyHSpyHSG9kmxgwt02eeR6WlAJ+HzCWF08x4Z0hIDBffdcidHTLXBzrRDmWpWtCy6Bj7Npt4yvifrO6fOnhZN5f1KzqyzopXe7n1zhAp132ym+h1UGnrxPhKLlJNEn862Jn6eVFWP8RPXdWAqw2Si/9jyOKxVi8Jdc4eG3Kq4Xjnc75A/trrb4rU50IvYvp6E+FqLZFBMAOcEEDn+fpat3h53T9s9KMuKqesRwpnuUpaA1L6olDs+36BqiSVOUxx9u2sxh5B3Sm48QN3yP/WqXixRmabmXkx5uH4Ig9ltwS1F9VCZqKzAI7oKCDHChsfYwXqwxEnbqRNRRQ=
  edge: true
  on:
    tags: true

script:
  - npm test

after_success:
  - npm run coveralls
