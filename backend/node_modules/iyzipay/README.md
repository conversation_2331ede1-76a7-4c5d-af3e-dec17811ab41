# iyzipay-node

[![NPM version](https://img.shields.io/npm/v/iyzipay.svg)](https://www.npmjs.com/package/iyzipay)

You can sign up for an iyzico account at [https://iyzico.com](https://iyzico.com)

# Requirements

Node.js v0.10.0 or later

# Installation

`npm install iyzipay`

# Usage

### Initialization

```js
var Iyzipay = require('iyzipay');

var iyzipay = new Iyzipay({
    apiKey: 'your api key',
    secretKey: 'your secret key',
    uri: 'https://sandbox-api.iyzipay.com'
});
```

As you can see, credentials information provided while creating new instance of Iyzipay class.
If you do not provide iyzipay credentials, default values will be fetched from environment variables
by following names.

```js
   IYZIPAY_URI
   IYZIPAY_API_KEY
   IYZIPAY_SECRET_KEY
```

In other words, you can initialize Iyzipay object like following:

```js
var iyzipay = new Iyzipay();
```

### Sample Usage

```js
var Iyzipay = require('iyzipay');

var iyzipay = new Iyzipay({
    apiKey: 'your api key',
    secretKey: 'your secret key',
    uri: 'https://sandbox-api.iyzipay.com'
});

var request = {
    locale: Iyzipay.LOCALE.TR,
    conversationId: '123456789',
    price: '1',
    paidPrice: '1.2',
    currency: Iyzipay.CURRENCY.TRY,
    installment: '1',
    basketId: 'B67832',
    paymentChannel: Iyzipay.PAYMENT_CHANNEL.WEB,
    paymentGroup: Iyzipay.PAYMENT_GROUP.PRODUCT,
    paymentCard: {
        cardHolderName: 'John Doe',
        cardNumber: '****************',
        expireMonth: '12',
        expireYear: '2030',
        cvc: '123',
        registerCard: '0'
    },
    buyer: {
        id: 'BY789',
        name: 'John',
        surname: 'Doe',
        gsmNumber: '+905350000000',
        email: '<EMAIL>',
        identityNumber: '74300864791',
        lastLoginDate: '2015-10-05 12:43:35',
        registrationDate: '2013-04-21 15:12:09',
        registrationAddress: 'Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1',
        ip: '************',
        city: 'Istanbul',
        country: 'Turkey',
        zipCode: '34732'
    },
    shippingAddress: {
        contactName: 'Jane Doe',
        city: 'Istanbul',
        country: 'Turkey',
        address: 'Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1',
        zipCode: '34742'
    },
    billingAddress: {
        contactName: 'Jane Doe',
        city: 'Istanbul',
        country: 'Turkey',
        address: 'Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1',
        zipCode: '34742'
    },
    basketItems: [
        {
            id: 'BI101',
            name: 'Binocular',
            category1: 'Collectibles',
            category2: 'Accessories',
            itemType: Iyzipay.BASKET_ITEM_TYPE.PHYSICAL,
            price: '0.3'
        },
        {
            id: 'BI102',
            name: 'Game code',
            category1: 'Game',
            category2: 'Online Game Items',
            itemType: Iyzipay.BASKET_ITEM_TYPE.VIRTUAL,
            price: '0.5'
        },
        {
            id: 'BI103',
            name: 'Usb',
            category1: 'Electronics',
            category2: 'Usb / Cable',
            itemType: Iyzipay.BASKET_ITEM_TYPE.PHYSICAL,
            price: '0.2'
        }
    ]
};

iyzipay.payment.create(request, function (err, result) {
    console.log(err, result);
    done();
});
```

You can see further examples in `test/samples` folder, and run them by `npm run-script samples`

### Mock test cards

Test cards that can be used to simulate a *successful* payment:

Card Number      | Bank                       | Card Type
-----------      | ----                       | ---------
**************** | Akbank                     | Master Card (Debit)  
**************** | Akbank                     | Master Card (Credit)  
**************** | Denizbank                  | Visa (Debit)  
**************** | Denizbank                  | Visa (Credit)
**************** | Denizbank Bonus            | Visa (Credit)  
**************** | Finansbank                 | Visa (Debit)  
**************** | Finansbank                 | Master Card (Credit)  
**************** | Finansbank                 | Troy (Debit)  
**************** | Finansbank                 | Troy (Credit)  
**************** | Garanti Bankası            | Master Card (Debit)  
**************** | Garanti Bankası            | Master Card (Credit)  
***************  | Garanti Bankası            | American Express  
**************** | Halkbank                   | Visa (Debit)  
**************** | Halkbank                   | Master Card (Credit)  
**************** | HSBC Bank                  | Visa (Debit)  
**************** | HSBC Bank                  | Master Card (Credit)  
**************** | Türkiye İş Bankası         | Master Card (Debit)  
**************** | Türkiye İş Bankası         | Visa (Credit)  
**************** | Vakıfbank                  | Visa (Debit)  
**************** | Vakıfbank                  | Visa (Credit)  
**************** | Yapı ve Kredi Bankası      | Master Card (Debit)  
**************** | Yapı ve Kredi Bankası      | Master Card (Credit)  

*Cross border* test cards:

Card Number      | Country
-----------      | -------
**************** | Non-Turkish (Debit)
**************** | Non-Turkish (Credit)    

Test cards to get specific *error* codes:

Card Number       | Description
-----------       | -----------
****************  | Success but cannot be cancelled, refund or post auth
****************  | Not sufficient funds
****************  | Do not honour
****************  | Invalid transaction
****************  | Lost card
****************  | Stolen card
****************  | Expired card
****************  | Invalid cvc2
****************  | Not permitted to card holder
****************  | Not permitted to terminal
****************  | Fraud suspect
****************  | Pickup card
****************  | General error
****************  | Success but mdStatus is 0
****************  | Success but mdStatus is 4
****************  | 3dsecure initialize failed

### Mock APM Accounts

Mock APM Accounts that can be used to simulate a payment with alternative payment method:

Account Holder Name     | Description
-------------------     | -----------
success                 | Succeeded payment after succeeded initialize
fail-after-init         | Failed payment after succeeded initialize
error                   | Failed initialize

# Testing

You need to have [mocha](https://mochajs.org/) installed on your machine in order to run tests.

`npm test`

# Author

Originally by [Huseyin Babal](https://github.com/huseyinbabal) (<EMAIL>). Now officially maintained by iyzico.
