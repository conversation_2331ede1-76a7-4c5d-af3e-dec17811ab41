name: Update Release Workflow
on:
  push:
    branches: [ "master" ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Update Release Number
        run: |
          set -e
          
          # Retrieve the current release number
          current_release=$(cat VERSION)

          # Increment the release number
          new_release=$(echo $current_release | awk -F. -v OFS=. '{$NF++;print}')

          # Set the new release number as an environment variable
          echo "NEW_RELEASE=${new_release}" >> $GITHUB_ENV
          
          # Change version number on files
          sed -i -e's/'$current_release'/'$new_release'/g' package.json lib/IyzipayResource.js VERSION

      - name: Github Tasks
        run: |
          set -e
          git config user.name "iyzico-ci"
          git config user.email <EMAIL>
          git add .
          git commit -m "Automatic commit by iyzico-ci v${{env.NEW_RELEASE}}"
          git push origin master
          git tag -a v${{env.NEW_RELEASE}} -m "Automatic tag by iyzico-ci v${{env.NEW_RELEASE}}"
          git push origin v${{env.NEW_RELEASE}}
          
      - name: Github Release
        run: |
          # Use the new release version in release creation step
          echo "Creating release ${{env.NEW_RELEASE}}..."
          result=$( curl -# -XPOST -H "Authorization: token ${{secrets.TOKEN_GITHUB}}" -H "Content-Type: application/json" -H "Accept:application/json" --data-binary '{"tag_name": "v${{env.NEW_RELEASE}}","target_commitish": "master","name": "iyzipay-node ${{env.NEW_RELEASE}}","body": "version ${{env.NEW_RELEASE}}","draft": false,"prerelease": false}' https://api.github.com/repos/iyzico/iyzipay-node/releases )
          echo $result
          echo "GitHub release ${{env.NEW_RELEASE}} created!"

      - uses: actions/setup-node@v4
        with:
          node-version: '14.x'
          registry-url: 'https://registry.npmjs.org'
      
      - name: NPM publish
        run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_ALL_PACKAGE_TOKEN}}
