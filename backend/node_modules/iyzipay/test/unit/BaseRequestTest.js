var assert = require('assert'),
    should = require('should'),
    BaseRequest = require('../../lib/requests/BaseRequest');

describe('BaseRequest', function () {

    before(function () {
        request = new BaseRequest;
    });

    it('should generate request string for basic request', function (done) {
        var requestString = request._generateRequestString({
            name: "<PERSON>",
            surname: "<PERSON><PERSON>"
        });
        requestString.should.be.equal("[name=<PERSON>,surname=<PERSON><PERSON>]");
        done();
    });

    it('should generate request string for request that has array values', function (done) {
        var requestString = request._generateRequestString({
            name: "<PERSON>",
            surname: "<PERSON><PERSON>",
            friends: [{name: "<PERSON>"}, {name: "<PERSON>"}]
        });
        requestString.should.be.equal("[name=<PERSON>,surname=<PERSON><PERSON>,friends=[[name=<PERSON>], [name=<PERSON>]]]");
        done();
    });
});
