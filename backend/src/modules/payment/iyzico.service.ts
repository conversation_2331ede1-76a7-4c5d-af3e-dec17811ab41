import Iyzipay from 'iyzipay';

export interface IyzicoConfig {
  apiKey: string;
  secretKey: string;
  uri: string;
}

export interface PaymentRequest {
  price: string;
  paidPrice: string;
  currency: string;
  installment: string;
  basketId: string;
  conversationId: string;
  buyer: {
    id: string;
    name: string;
    surname: string;
    gsmNumber: string;
    email: string;
    identityNumber: string;
    registrationAddress: string;
    ip: string;
    city: string;
    country: string;
    zipCode: string;
  };
  shippingAddress: {
    contactName: string;
    city: string;
    country: string;
    address: string;
    zipCode: string;
  };
  billingAddress: {
    contactName: string;
    city: string;
    country: string;
    address: string;
    zipCode: string;
  };
  basketItems: Array<{
    id: string;
    name: string;
    category1: string;
    category2: string;
    itemType: string;
    price: string;
  }>;
  paymentCard: {
    cardHolderName: string;
    cardNumber: string;
    expireMonth: string;
    expireYear: string;
    cvc: string;
    registerCard: string;
  };
}

export interface CheckoutFormRequest {
  locale: string;
  conversationId: string;
  price: string;
  paidPrice: string;
  currency: string;
  basketId: string;
  paymentGroup: string;
  callbackUrl: string;
  enabledInstallments: string[];
  buyer: {
    id: string;
    name: string;
    surname: string;
    gsmNumber: string;
    email: string;
    identityNumber: string;
    registrationAddress: string;
    ip: string;
    city: string;
    country: string;
    zipCode: string;
  };
  shippingAddress: {
    contactName: string;
    city: string;
    country: string;
    address: string;
    zipCode: string;
  };
  billingAddress: {
    contactName: string;
    city: string;
    country: string;
    address: string;
    zipCode: string;
  };
  basketItems: Array<{
    id: string;
    name: string;
    category1: string;
    category2: string;
    itemType: string;
    price: string;
  }>;
}

export class IyzicoService {
  private iyzipay: any;

  constructor(config: IyzicoConfig) {
    this.iyzipay = new Iyzipay({
      apiKey: config.apiKey,
      secretKey: config.secretKey,
      uri: config.uri
    });
  }

  // Direkt ödeme işlemi
  async createPayment(request: PaymentRequest): Promise<any> {
    return new Promise((resolve, reject) => {
      this.iyzipay.payment.create(request, (err: any, result: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(result);
        }
      });
    });
  }

  // Checkout form ile ödeme başlatma
  async initializeCheckoutForm(request: CheckoutFormRequest): Promise<any> {
    return new Promise((resolve, reject) => {
      this.iyzipay.checkoutFormInitialize.create(request, (err: any, result: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(result);
        }
      });
    });
  }

  // Checkout form sonucunu alma
  async retrieveCheckoutForm(token: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.iyzipay.checkoutForm.retrieve({
        locale: 'tr',
        token: token
      }, (err: any, result: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(result);
        }
      });
    });
  }

  // Ödeme durumunu sorgulama
  async retrievePayment(paymentId: string, conversationId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.iyzipay.payment.retrieve({
        locale: 'tr',
        conversationId: conversationId,
        paymentId: paymentId
      }, (err: any, result: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(result);
        }
      });
    });
  }

  // İptal işlemi
  async cancelPayment(paymentId: string, ip: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.iyzipay.cancel.create({
        locale: 'tr',
        paymentId: paymentId,
        ip: ip
      }, (err: any, result: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(result);
        }
      });
    });
  }

  // İade işlemi
  async refundPayment(paymentTransactionId: string, price: string, ip: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.iyzipay.refund.create({
        locale: 'tr',
        paymentTransactionId: paymentTransactionId,
        price: price,
        ip: ip,
        currency: 'TRY'
      }, (err: any, result: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(result);
        }
      });
    });
  }

  // Taksit seçeneklerini alma
  async retrieveInstallmentInfo(binNumber: string, price: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.iyzipay.installmentInfo.retrieve({
        locale: 'tr',
        binNumber: binNumber,
        price: price
      }, (err: any, result: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(result);
        }
      });
    });
  }

  // Abonelik için ödeme verisi hazırlama
  static prepareSubscriptionPaymentData(
    userId: string,
    subscriptionType: 'monthly' | 'yearly',
    userInfo: any,
    ip: string
  ): CheckoutFormRequest {
    const pricing = {
      monthly: { price: 99, name: 'Aylık Abonelik' },
      yearly: { price: 999, name: 'Yıllık Abonelik' }
    };

    const selectedPlan = pricing[subscriptionType];
    const conversationId = `SUB_${userId}_${Date.now()}`;
    const basketId = `BASKET_${conversationId}`;

    return {
      locale: 'tr',
      conversationId,
      price: selectedPlan.price.toString(),
      paidPrice: selectedPlan.price.toString(),
      currency: 'TRY',
      basketId,
      paymentGroup: 'SUBSCRIPTION',
      callbackUrl: `${process.env.FRONTEND_URL}/subscription/payment-callback`,
      enabledInstallments: ['1'],
      buyer: {
        id: userId,
        name: userInfo.name.split(' ')[0] || 'Ad',
        surname: userInfo.name.split(' ').slice(1).join(' ') || 'Soyad',
        gsmNumber: userInfo.phone || '+905350000000',
        email: userInfo.email,
        identityNumber: '11111111111', // Test için sabit değer
        registrationAddress: userInfo.address || 'Test Adres',
        ip: ip,
        city: userInfo.city || 'Istanbul',
        country: 'Turkey',
        zipCode: userInfo.zipCode || '34000'
      },
      shippingAddress: {
        contactName: userInfo.name,
        city: userInfo.city || 'Istanbul',
        country: 'Turkey',
        address: userInfo.address || 'Test Adres',
        zipCode: userInfo.zipCode || '34000'
      },
      billingAddress: {
        contactName: userInfo.name,
        city: userInfo.city || 'Istanbul',
        country: 'Turkey',
        address: userInfo.address || 'Test Adres',
        zipCode: userInfo.zipCode || '34000'
      },
      basketItems: [
        {
          id: `ITEM_${subscriptionType}`,
          name: selectedPlan.name,
          category1: 'Subscription',
          category2: 'Software',
          itemType: 'VIRTUAL',
          price: selectedPlan.price.toString()
        }
      ]
    };
  }

  // Proforma hesaplama için ödeme verisi hazırlama
  static prepareProformaPaymentData(
    userId: string,
    calculationId: string,
    amount: number,
    currency: string = 'TRY',
    userInfo: any,
    ip: string
  ): CheckoutFormRequest {
    const conversationId = `CALC_${userId}_${calculationId}_${Date.now()}`;
    const basketId = `BASKET_${conversationId}`;

    return {
      locale: 'tr',
      conversationId,
      price: amount.toString(),
      paidPrice: amount.toString(),
      currency: currency,
      basketId,
      paymentGroup: 'CALCULATION',
      callbackUrl: `${process.env.FRONTEND_URL}/calculation/payment-callback`,
      enabledInstallments: ['1'],
      buyer: {
        id: userId,
        name: userInfo.name.split(' ')[0] || 'Ad',
        surname: userInfo.name.split(' ').slice(1).join(' ') || 'Soyad',
        gsmNumber: userInfo.phone || '+905350000000',
        email: userInfo.email,
        identityNumber: '11111111111', // Test için sabit değer
        registrationAddress: userInfo.address || 'Test Adres',
        ip: ip,
        city: userInfo.city || 'Istanbul',
        country: 'Turkey',
        zipCode: userInfo.zipCode || '34000'
      },
      shippingAddress: {
        contactName: userInfo.name,
        city: userInfo.city || 'Istanbul',
        country: 'Turkey',
        address: userInfo.address || 'Test Adres',
        zipCode: userInfo.zipCode || '34000'
      },
      billingAddress: {
        contactName: userInfo.name,
        city: userInfo.city || 'Istanbul',
        country: 'Turkey',
        address: userInfo.address || 'Test Adres',
        zipCode: userInfo.zipCode || '34000'
      },
      basketItems: [
        {
          id: `CALC_${calculationId}`,
          name: 'Proforma Hesaplama',
          category1: 'Calculation',
          category2: 'Maritime',
          itemType: 'VIRTUAL',
          price: amount.toString()
        }
      ]
    };
  }
}
