import { Router } from 'express';
import { PaymentController } from './payment.controller';
import { protect } from '../../middleware/auth';

const router = Router();

// Public routes
router.post('/callback', PaymentController.handlePaymentCallback);
router.post('/proforma/callback', PaymentController.handleProformaPaymentCallback);
router.get('/installment-info', PaymentController.getInstallmentInfo);

// Test endpoint (sadece development için)
if (process.env.NODE_ENV === 'development') {
  router.get('/test-connection', PaymentController.testIyzicoConnection);
}

// Protected routes
router.use(protect); // Tüm aşağıdaki routes için auth gerekli

// Abonelik ödeme işlemleri
router.post('/subscription/initialize', PaymentController.initializeSubscriptionPayment);
router.get('/status/:paymentId/:conversationId', PaymentController.getPaymentStatus);

// Proforma ödeme işlemleri
router.post('/proforma/initialize', PaymentController.initializeProformaPayment);

// Ödeme yönetimi
router.post('/cancel', PaymentController.cancelPayment);
router.post('/refund', PaymentController.refundPayment);

export default router;
