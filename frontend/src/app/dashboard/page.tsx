'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { userAPI, calculationAPI, authAPI } from '@/lib/api';

interface UserProfile {
  name: string;
  email: string;
  subscriptionEndDate?: string;
}

interface Calculation {
  _id: string;
  title: string;
  type: string;
  createdAt: string;
  result?: any;
}

export default function Dashboard() {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [calculations, setCalculations] = useState<Calculation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const router = useRouter();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Check if user is authenticated
        const token = document.cookie.split(';').find(cookie => cookie.trim().startsWith('token='));
        if (!token) {
          router.push('/login');
          return;
        }

        // Fetch user profile and calculations
        const [profileResponse, calculationsResponse] = await Promise.all([
          userAPI.getProfile(),
          calculationAPI.getUserCalculations()
        ]);

        setUser(profileResponse.data);
        setCalculations(calculationsResponse.data?.calculations || []);
      } catch (err: any) {
        console.error('Dashboard fetch error:', err);
        if (err.message.includes('401') || err.message.includes('Unauthorized')) {
          router.push('/login');
        } else {
          setError(err.message || 'Failed to load dashboard data');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [router]);

  const handleLogout = async () => {
    try {
      await authAPI.logout();
      document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
      router.push('/login');
    } catch (err) {
      console.error('Logout error:', err);
      // Force logout even if API call fails
      document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
      router.push('/login');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                QuickPDA Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Welcome back, {user?.name}!
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Link 
                href="/profile" 
                className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
              >
                Profile
              </Link>
              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Link href="/calculation/maritime" className="block">
              <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-600 rounded-md flex items-center justify-center">
                        <span className="text-white font-bold">⚓</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                          Maritime Calculation
                        </dt>
                        <dd className="text-lg font-medium text-gray-900 dark:text-white">
                          New Proforma
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </Link>

            <Link href="/subscriptions" className="block">
              <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-600 rounded-md flex items-center justify-center">
                        <span className="text-white font-bold">💳</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                          Subscription
                        </dt>
                        <dd className="text-lg font-medium text-gray-900 dark:text-white">
                          Manage Plan
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </Link>

            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-purple-600 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold">📊</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                        Total Calculations
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 dark:text-white">
                        {calculations.length}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Calculations */}
          <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                Recent Calculations
              </h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                Your latest proforma calculations
              </p>
            </div>
            <ul className="divide-y divide-gray-200 dark:divide-gray-700">
              {calculations.length === 0 ? (
                <li className="px-4 py-4 text-center text-gray-500 dark:text-gray-400">
                  No calculations yet. <Link href="/calculation/maritime" className="text-blue-600 hover:text-blue-500">Create your first calculation</Link>
                </li>
              ) : (
                calculations.slice(0, 5).map((calculation) => (
                  <li key={calculation._id}>
                    <Link href={`/calculation/${calculation._id}`} className="block hover:bg-gray-50 dark:hover:bg-gray-700">
                      <div className="px-4 py-4 sm:px-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <p className="text-sm font-medium text-blue-600 dark:text-blue-400 truncate">
                              {calculation.title || 'Untitled Calculation'}
                            </p>
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                              {calculation.type}
                            </span>
                          </div>
                          <div className="ml-2 flex-shrink-0 flex">
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {new Date(calculation.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </div>
                    </Link>
                  </li>
                ))
              )}
            </ul>
            {calculations.length > 5 && (
              <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 text-center">
                <Link href="/calculations" className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400">
                  View all calculations →
                </Link>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
